{"__meta": {"id": "Xdf13b26cde16788a6cf2c26d834cebfa", "datetime": "2025-08-08 17:43:48", "utime": **********.197372, "method": "GET", "uri": "/annex/public/loan", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754667827.113065, "end": **********.197393, "duration": 1.0843279361724854, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1754667827.113065, "relative_start": 0, "end": 1754667827.846944, "relative_end": 1754667827.846944, "duration": 0.7338790893554688, "duration_str": "734ms", "params": [], "collector": null}, {"label": "Application", "start": 1754667827.84914, "relative_start": 0.7360749244689941, "end": **********.197395, "relative_end": 2.1457672119140625e-06, "duration": 0.3482551574707031, "duration_str": "348ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 47855656, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "loan::themes.adminlte.loan.index (\\Modules\\Loan\\Resources\\views\\themes\\adminlte\\loan\\index.blade.php)", "param_count": 1, "params": ["data"], "type": "blade"}, {"name": "pagination::bootstrap-4 (\\vendor\\laravel\\framework\\src\\Illuminate\\Pagination\\resources\\views\\bootstrap-4.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade"}, {"name": "core::layouts.master (\\themes\\adminlte\\views\\layouts\\master.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.top_nav (\\themes\\adminlte\\views\\partials\\top_nav.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::menu.admin (\\themes\\adminlte\\views\\menu\\admin.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.flash.message (\\themes\\adminlte\\views\\partials\\flash\\message.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}, {"name": "core::partials.footer (\\themes\\adminlte\\views\\partials\\footer.blade.php)", "param_count": 11, "params": ["__env", "app", "company_name", "company_logo", "system_version", "errors", "data", "__currentLoopData", "key", "loop", "balance"], "type": "blade"}]}, "route": {"uri": "GET loan", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanController@index", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:62-153"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.14382999999999999, "accumulated_duration_str": "144ms", "statements": [{"sql": "select * from `users` where `id` = 9 limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0068, "duration_str": "6.8ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.01068, "duration_str": "10.68ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 9 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00267, "duration_str": "2.67ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.0039900000000000005, "duration_str": "3.99ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 9 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.00176, "duration_str": "1.76ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select count(*) as aggregate from (select concat(clients.first_name,' ',clients.last_name) client,concat(users.first_name,' ',users.last_name) loan_officer,loans.id,loans.client_id,loans.applied_amount,loans.principal,loans.disbursed_on_date,loans.expected_maturity_date,loan_products.name loan_product,loans.status,loans.decimals,branches.name branch, SUM(loan_repayment_schedules.principal) total_principal, SUM(loan_repayment_schedules.principal_written_off_derived) principal_written_off_derived, SUM(loan_repayment_schedules.principal_repaid_derived) principal_repaid_derived, SUM(loan_repayment_schedules.interest) total_interest, SUM(loan_repayment_schedules.interest_waived_derived) interest_waived_derived,SUM(loan_repayment_schedules.interest_written_off_derived) interest_written_off_derived,  SUM(loan_repayment_schedules.interest_repaid_derived) interest_repaid_derived,SUM(loan_repayment_schedules.fees) total_fees, SUM(loan_repayment_schedules.fees_waived_derived) fees_waived_derived, SUM(loan_repayment_schedules.fees_written_off_derived) fees_written_off_derived, SUM(loan_repayment_schedules.fees_repaid_derived) fees_repaid_derived,SUM(loan_repayment_schedules.penalties) total_penalties, SUM(loan_repayment_schedules.penalties_waived_derived) penalties_waived_derived, SUM(loan_repayment_schedules.penalties_written_off_derived) penalties_written_off_derived, SUM(loan_repayment_schedules.penalties_repaid_derived) penalties_repaid_derived from `loans` left join `clients` on `clients`.`id` = `loans`.`client_id` left join `loan_repayment_schedules` on `loan_repayment_schedules`.`loan_id` = `loans`.`id` left join `loan_products` on `loan_products`.`id` = `loans`.`loan_product_id` left join `branches` on `branches`.`id` = `loans`.`branch_id` left join `users` on `users`.`id` = `loans`.`loan_officer_id` where `due_date` between '2025-08-04 00:00:00' and '2025-08-10 23:59:59' group by `loans`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["2025-08-04 00:00:00", "2025-08-10 23:59:59"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 107}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.09687, "duration_str": "96.87ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:107", "connection": "annex"}, {"sql": "select concat(clients.first_name,' ',clients.last_name) client,concat(users.first_name,' ',users.last_name) loan_officer,loans.id,loans.client_id,loans.applied_amount,loans.principal,loans.disbursed_on_date,loans.expected_maturity_date,loan_products.name loan_product,loans.status,loans.decimals,branches.name branch, SUM(loan_repayment_schedules.principal) total_principal, SUM(loan_repayment_schedules.principal_written_off_derived) principal_written_off_derived, SUM(loan_repayment_schedules.principal_repaid_derived) principal_repaid_derived, SUM(loan_repayment_schedules.interest) total_interest, SUM(loan_repayment_schedules.interest_waived_derived) interest_waived_derived,SUM(loan_repayment_schedules.interest_written_off_derived) interest_written_off_derived,  SUM(loan_repayment_schedules.interest_repaid_derived) interest_repaid_derived,SUM(loan_repayment_schedules.fees) total_fees, SUM(loan_repayment_schedules.fees_waived_derived) fees_waived_derived, SUM(loan_repayment_schedules.fees_written_off_derived) fees_written_off_derived, SUM(loan_repayment_schedules.fees_repaid_derived) fees_repaid_derived,SUM(loan_repayment_schedules.penalties) total_penalties, SUM(loan_repayment_schedules.penalties_waived_derived) penalties_waived_derived, SUM(loan_repayment_schedules.penalties_written_off_derived) penalties_written_off_derived, SUM(loan_repayment_schedules.penalties_repaid_derived) penalties_repaid_derived from `loans` left join `clients` on `clients`.`id` = `loans`.`client_id` left join `loan_repayment_schedules` on `loan_repayment_schedules`.`loan_id` = `loans`.`id` left join `loan_products` on `loan_products`.`id` = `loans`.`loan_product_id` left join `branches` on `branches`.`id` = `loans`.`branch_id` left join `users` on `users`.`id` = `loans`.`loan_officer_id` where `due_date` between '2025-08-04 00:00:00' and '2025-08-10 23:59:59' group by `loans`.`id` limit 10 offset 0", "type": "query", "params": [], "bindings": ["2025-08-04 00:00:00", "2025-08-10 23:59:59"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 107}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.01526, "duration_str": "15.26ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:107", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 27}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select count(*) as aggregate from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php", "line": 23}, {"index": 17, "namespace": "view", "name": "core::partials.top_nav", "line": 30}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:23", "connection": "annex"}, {"sql": "select * from `notifications` where `notifications`.`notifiable_id` = 9 and `notifications`.`notifiable_id` is not null and `notifications`.`notifiable_type` = 'Modules\\User\\Entities\\User' and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::partials.top_nav", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "view::core::partials.top_nav:31", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_logo' limit 1", "type": "query", "params": [], "bindings": ["core.company_logo"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 3}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "view::core::menu.admin:3", "connection": "annex"}, {"sql": "select * from `settings` where `setting_key` = 'core.company_name' limit 1", "type": "query", "params": [], "bindings": ["core.company_name"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "core::menu.admin", "line": 8}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00055, "duration_str": "550μs", "stmt_id": "view::core::menu.admin:8", "connection": "annex"}, {"sql": "select * from `menus` where `is_parent` = 1 order by `menu_order` asc", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.0013700000000000001, "duration_str": "1.37ms", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}, {"sql": "select * from `menus` where `menus`.`parent_id` in (1, 9, 10, 12, 13, 17, 18, 21, 24, 28, 31, 36, 37, 44, 49, 54, 58, 61, 66, 71) order by `menu_order` asc", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "core::menu.admin", "line": 31}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LivewireViewCompilerEngine.php", "line": 32}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 61}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "view::core::menu.admin:31", "connection": "annex"}]}, "models": {"data": {"Modules\\Core\\Entities\\Menu": 73, "Modules\\Setting\\Entities\\Setting": 2, "Modules\\Loan\\Entities\\Loan": 10, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 87}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON>@mjy3.com\"\n  \"user\" => array:25 [\n    \"id\" => 9\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"<PERSON>\"\n    \"phone\" => \"+233555928981\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"This access is given for the purpose of internal functions only\"\n    \"photo\" => \"kI83IU45zpa8IywCrNXJY6rulxWk3OSLHuGt0dCg.jpeg\"\n    \"created_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"updated_at\" => \"2025-01-17T08:13:41.000000Z\"\n    \"full_name\" => \"<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 9\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n    \"unread_notifications\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 41, "messages": [{"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1905099686 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905099686\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754667827.978145}, {"message": "[ability => loan.loans.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-312324641 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-312324641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.102549}, {"message": "[ability => dashboard.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dashboard.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.131058}, {"message": "[\n  ability => accounting.chart_of_accounts.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"34 characters\">accounting.chart_of_accounts.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.133191}, {"message": "[\n  ability => accounting.journal_entries.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">accounting.journal_entries.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.134502}, {"message": "[ability => branch.branches.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">branch.branches.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135785}, {"message": "[ability => branch.branches.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1635069760 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">branch.branches.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635069760\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.137065}, {"message": "[ability => client.clients.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1480784607 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">client.clients.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480784607\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13835}, {"message": "[ability => client.clients.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1620677237 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">client.clients.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1620677237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.139632}, {"message": "[ability => payroll.payroll.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-92619145 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">payroll.payroll.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-92619145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.140897}, {"message": "[\n  ability => communication.campaigns.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1069641323 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">communication.campaigns.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069641323\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.14218}, {"message": "[\n  ability => communication.campaigns.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1451857415 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">communication.campaigns.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451857415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.143428}, {"message": "[\n  ability => communication.logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-384097934 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">communication.logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384097934\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.14461}, {"message": "[\n  ability => communication.sms_gateways.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-644758236 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">communication.sms_gateways.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644758236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.145804}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-39322777 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39322777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147023}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1366337169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366337169\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148411}, {"message": "[ability => loan.loans.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-459569887 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">loan.loans.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-459569887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.149637}, {"message": "[\n  ability => loan.loans.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1431881212 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">loan.loans.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431881212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.150844}, {"message": "[\n  ability => loan.loans.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1110339200 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">loan.loans.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110339200\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.152023}, {"message": "[ability => loan.loans.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-173108567 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">loan.loans.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-173108567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.153222}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-542729969 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-542729969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.15443}, {"message": "[\n  ability => loan.loans.reports.collection_sheet,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1283018391 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"35 characters\">loan.loans.reports.collection_sheet</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283018391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.155624}, {"message": "[ability => reports.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1282338969 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">reports.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1282338969\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156834}, {"message": "[ability => expense.expenses.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1083759761 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">expense.expenses.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083759761\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.158039}, {"message": "[ability => savings.savings.index, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-660152771 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">savings.savings.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660152771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159386}, {"message": "[ability => savings.savings.create, result => true, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-330090821 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">savings.savings.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330090821\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160589}, {"message": "[\n  ability => savings.savings.products.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-332381794 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">savings.savings.products.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332381794\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.161793}, {"message": "[\n  ability => savings.savings.charges.index,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-580617857 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"29 characters\">savings.savings.charges.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580617857\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163011}, {"message": "[\n  ability => customfield.custom_fields.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-73255088 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">customfield.custom_fields.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73255088\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.164465}, {"message": "[\n  ability => customfield.custom_fields.create,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-431552877 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customfield.custom_fields.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431552877\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.165644}, {"message": "[ability => income.income.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1824349517 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">income.income.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1824349517\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.16682}, {"message": "[ability => user.users.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-56788010 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.users.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-56788010\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.168104}, {"message": "[ability => user.users.create, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-915142078 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">user.users.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915142078\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.169282}, {"message": "[ability => user.roles.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1752644251 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.roles.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752644251\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.170451}, {"message": "[ability => core.modules.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1661455144 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">core.modules.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1661455144\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.171636}, {"message": "[ability => asset.assets.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-193995790 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">asset.assets.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193995790\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.172848}, {"message": "[ability => share.shares.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2124807442 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">share.shares.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124807442\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.17403}, {"message": "[ability => setting.setting.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1739520812 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">setting.setting.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739520812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.175204}, {"message": "[ability => core.menu.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-682759366 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">core.menu.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682759366\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.176383}, {"message": "[ability => core.themes.index, result => null, user => 9, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1031998103 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">core.themes.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1031998103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.177553}, {"message": "[\n  ability => activitylog.activity_logs.index,\n  result => null,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-81360228 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"31 characters\">activitylog.activity_logs.index</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81360228\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.178722}]}, "session": {"_token": "FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/loan\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "9", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f960b42-8d17-42f7-8c8a-a775761271f3\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan", "status_code": "<pre class=sf-dump id=sf-dump-1151022021 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1151022021\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1884222361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1884222361\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1297055041 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1297055041\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1239053797 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://*************/annex/public/loan/801/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IjJoSDg0bllYRStpMjQwWDA5TFpva3c9PSIsInZhbHVlIjoib2FCQzVsSFlSMUxGMWJFR3dHcC9TbElBNkhYd2UxSU85bDZzSytMcGNoMFBDaHExdjhtdVJaT1VIT1BvTVF2dzNRcXEzUmZkN3k0c285anF5Z2JsRUFmWFY1MFdZMzVvdEFWOHZqMnhDSXQ0N1BWMWdRRlgrYnBqVkVSMktMQVUiLCJtYWMiOiJjZGEyYmE4YTJjMGM4ZWNmZDA0OWIwMmQzZWNiNTRlODk5NTQ5ZjA1MzI2MjYxMzRlMTUyNGY2YmNhZjQ1Zjc0In0%3D; mjy3_micro_loans_session=eyJpdiI6IlFxT0ptVlRTanl2NUhaSUZKUG5sYnc9PSIsInZhbHVlIjoiMXhPbGxqZ3FEeXVUOWpRdWRBQ2d0U1Fvakx3Ryt3L0VGTkk5NnQ3ZEt4NnB0eTQwSStnVWUzeW4zZ052aklVeXNJcmdKdUduRTlIK25BSEZrK1pGcmhmMTVsZk9PcnMwSGYvMUFueHltMld3K3VKSHFlWUM1TDBmenF6bnVXeXMiLCJtYWMiOiI2YTE5YzNiNTFiMmI1ZDgxNzI2NjdmZGU3MzdlZDZhZjE1MDlmMTJjMTQyM2Q5NzQ3NGZiNDk0NTE3YTI1MTZmIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239053797\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-760689095 data-indent-pad=\"  \"><span class=sf-dump-note>array:50</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"47 characters\">http://*************/annex/public/loan/801/show</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6IjJoSDg0bllYRStpMjQwWDA5TFpva3c9PSIsInZhbHVlIjoib2FCQzVsSFlSMUxGMWJFR3dHcC9TbElBNkhYd2UxSU85bDZzSytMcGNoMFBDaHExdjhtdVJaT1VIT1BvTVF2dzNRcXEzUmZkN3k0c285anF5Z2JsRUFmWFY1MFdZMzVvdEFWOHZqMnhDSXQ0N1BWMWdRRlgrYnBqVkVSMktMQVUiLCJtYWMiOiJjZGEyYmE4YTJjMGM4ZWNmZDA0OWIwMmQzZWNiNTRlODk5NTQ5ZjA1MzI2MjYxMzRlMTUyNGY2YmNhZjQ1Zjc0In0%3D; mjy3_micro_loans_session=eyJpdiI6IlFxT0ptVlRTanl2NUhaSUZKUG5sYnc9PSIsInZhbHVlIjoiMXhPbGxqZ3FEeXVUOWpRdWRBQ2d0U1Fvakx3Ryt3L0VGTkk5NnQ3ZEt4NnB0eTQwSStnVWUzeW4zZ052aklVeXNJcmdKdUduRTlIK25BSEZrK1pGcmhmMTVsZk9PcnMwSGYvMUFueHltMld3K3VKSHFlWUM1TDBmenF6bnVXeXMiLCJtYWMiOiI2YTE5YzNiNTFiMmI1ZDgxNzI2NjdmZGU3MzdlZDZhZjE1MDlmMTJjMTQyM2Q5NzQ3NGZiNDk0NTE3YTI1MTZmIn0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">32502</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/annex/public/loan</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"18 characters\">/annex/public/loan</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754667827.1131</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754667827</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760689095\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-253108982 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yPsec4PrJ2v0O17ETRQJnf9a8nwYk8R7zJ9UNPPA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253108982\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-97251956 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 15:43:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6Ikg4enhRUStsb1B6ZzRxUmhUTm1Nb1E9PSIsInZhbHVlIjoiVWFlZ3M3TEdxcC9YWnR6MDhWL0NaNnFqMDFrbnpWR0NaRHdJS2E5MHpBTWpXcTByVXZoWmdrOWZ4NVNmeVpMc28rU0hpWmhsajN2dW9WTVdHZFE3Vk05cVdrMnhRblRkRFlvUjN3VGFiRDBMRkNObEhTMlZDWTFNTGdTS2IyVzAiLCJtYWMiOiIyMWMwYjgwNjdlY2Y0NTc3YzQxYWNjMDBhYWU1MjMwYTNkMzJlNTYzOWY3MTc3NDZjYjhkNzdkYzRhMDIzNTI4In0%3D; expires=Sat, 09-Aug-2025 11:43:48 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6ImxOSCtHZVdBeXhJN0dqOFVnSDlkckE9PSIsInZhbHVlIjoibnNOeEF1d0lxRis0bjFQK3RISzVCaVJvR0t1c3JoQWxXOVMzV3MyeUZnQjFmVnJhUzlMeXo1dFpYNXVzZmhKQTlSQVRmTVIvYVMxcUhvRk1ySnB6QndnSXNnZXJtRkN2eHJnMlFpYWVXREc4a1lUTVRYWHJ5bndob25jNnllbG4iLCJtYWMiOiIwYjc5NTI5MWYwNjFlYWIxNjI0OTA1Y2FjZGJmYjBjYzdhYmJlMjM2MDk5ZjcxNmNmNDllYWM2MTdhMGRkMDdkIn0%3D; expires=Sat, 09-Aug-2025 11:43:48 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6Ikg4enhRUStsb1B6ZzRxUmhUTm1Nb1E9PSIsInZhbHVlIjoiVWFlZ3M3TEdxcC9YWnR6MDhWL0NaNnFqMDFrbnpWR0NaRHdJS2E5MHpBTWpXcTByVXZoWmdrOWZ4NVNmeVpMc28rU0hpWmhsajN2dW9WTVdHZFE3Vk05cVdrMnhRblRkRFlvUjN3VGFiRDBMRkNObEhTMlZDWTFNTGdTS2IyVzAiLCJtYWMiOiIyMWMwYjgwNjdlY2Y0NTc3YzQxYWNjMDBhYWU1MjMwYTNkMzJlNTYzOWY3MTc3NDZjYjhkNzdkYzRhMDIzNTI4In0%3D; expires=Sat, 09-Aug-2025 11:43:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6ImxOSCtHZVdBeXhJN0dqOFVnSDlkckE9PSIsInZhbHVlIjoibnNOeEF1d0lxRis0bjFQK3RISzVCaVJvR0t1c3JoQWxXOVMzV3MyeUZnQjFmVnJhUzlMeXo1dFpYNXVzZmhKQTlSQVRmTVIvYVMxcUhvRk1ySnB6QndnSXNnZXJtRkN2eHJnMlFpYWVXREc4a1lUTVRYWHJ5bndob25jNnllbG4iLCJtYWMiOiIwYjc5NTI5MWYwNjFlYWIxNjI0OTA1Y2FjZGJmYjBjYzdhYmJlMjM2MDk5ZjcxNmNmNDllYWM2MTdhMGRkMDdkIn0%3D; expires=Sat, 09-Aug-2025 11:43:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97251956\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-41593809 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://*************/annex/public/loan</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-41593809\", {\"maxDepth\":0})</script>\n"}}
{"__meta": {"id": "Xd779c4b609f2f80ed9a54eb5196108ac", "datetime": "2025-08-08 17:33:08", "utime": 1754667188.245668, "method": "POST", "uri": "/annex/public/loan/801/repayment/store", "ip": "**************"}, "php": {"version": "7.4.22", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754667187.177259, "end": 1754667188.245688, "duration": 1.0684289932250977, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": 1754667187.177259, "relative_start": 0, "end": 1754667187.959207, "relative_end": 1754667187.959207, "duration": 0.7819480895996094, "duration_str": "782ms", "params": [], "collector": null}, {"label": "Application", "start": 1754667187.961331, "relative_start": 0.7840719223022461, "end": 1754667188.24569, "relative_end": 2.1457672119140625e-06, "duration": 0.2843592166900635, "duration_str": "284ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 47478624, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST loan/{id}/repayment/store", "middleware": "web", "controller": "Modules\\Loan\\Http\\Controllers\\LoanController@store_repayment", "namespace": "Modules\\Loan\\Http\\Controllers", "prefix": "/loan", "where": [], "file": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:1722-1761"}, "queries": {"nb_statements": 40, "nb_failed_statements": 0, "accumulated_duration": 0.07123, "accumulated_duration_str": "71.23ms", "statements": [{"sql": "select * from `users` where `id` = 9 limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 52}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 139}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:52", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'roles'", "type": "query", "params": [], "bindings": ["annex", "roles"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 26}, {"index": 17, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 47}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 23, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.01222, "duration_str": "12.22ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php:26", "connection": "annex"}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 9 and `model_has_roles`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 184}, {"index": 20, "namespace": null, "name": "\\Modules\\Portal\\Http\\Middleware\\CheckClientSession.php", "line": 21}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": "middleware", "name": "bindings", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:184", "connection": "annex"}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'annex' and table_name = 'permissions'", "type": "query", "params": [], "bindings": ["annex", "permissions"], "hints": [], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php", "line": 27}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 849}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 691}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 796}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 637}], "duration": 0.00415, "duration_str": "4.15ms", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php:27", "connection": "annex"}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 9 and `model_has_permissions`.`model_type` = 'Modules\\User\\Entities\\User'", "type": "query", "params": [], "bindings": ["9", "Modules\\User\\Entities\\User"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 285}, {"index": 20, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 139}, {"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 201}, {"index": 22, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 90}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 492}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php:285", "connection": "annex"}, {"sql": "select * from `loans` where `loans`.`id` = '801' limit 1", "type": "query", "params": [], "bindings": ["801"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 1730}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:1730", "connection": "annex"}, {"sql": "select * from `loan_products` where `loan_products`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 1730}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:1730", "connection": "annex"}, {"sql": "insert into `payment_details` (`created_by_id`, `payment_type_id`, `transaction_type`, `cheque_number`, `receipt`, `account_number`, `bank_name`, `routing_code`, `description`, `updated_at`, `created_at`) values (9, '1', 'loan_transaction', '', '', '', '', '', '', '2025-08-08 17:33:08', '2025-08-08 17:33:08')", "type": "query", "params": [], "bindings": ["9", "1", "loan_transaction", "", "", "", "", "", "", "2025-08-08 17:33:08", "2025-08-08 17:33:08"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 1742}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00328, "duration_str": "3.28ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:1742", "connection": "annex"}, {"sql": "insert into `loan_transactions` (`created_by_id`, `loan_id`, `payment_detail_id`, `name`, `loan_transaction_type_id`, `submitted_on`, `created_on`, `amount`, `credit`, `updated_at`, `created_at`) values (9, 801, 8531, 'Repayment', 2, '2025-08-08', '2025-08-08', '390', '390', '2025-08-08 17:33:08', '2025-08-08 17:33:08')", "type": "query", "params": [], "bindings": ["9", "801", "8531", "Repayment", "2", "2025-08-08", "2025-08-08", "390", "390", "2025-08-08 17:33:08", "2025-08-08 17:33:08"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 1753}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 691}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php:1753", "connection": "annex"}, {"sql": "insert into `activity_log` (`log_name`, `properties`, `causer_id`, `causer_type`, `subject_id`, `subject_type`, `description`, `updated_at`, `created_at`) values ('default', '{\\\"id\\\":10053}', 9, '<PERSON><PERSON><PERSON>\\User\\Entities\\User', 10053, 'Mo<PERSON><PERSON>\\Loan\\Entities\\LoanTransaction', 'Create Loan Repayment', '2025-08-08 17:33:08', '2025-08-08 17:33:08')", "type": "query", "params": [], "bindings": ["default", "{&quot;id&quot;:10053}", "9", "Modules\\User\\Entities\\User", "10053", "Modules\\Loan\\Entities\\LoanTransaction", "Create Loan Repayment", "2025-08-08 17:33:08", "2025-08-08 17:33:08"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php", "line": 161}, {"index": 16, "namespace": null, "name": "\\Modules\\Loan\\Http\\Controllers\\LoanController.php", "line": 1756}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0024300000000000003, "duration_str": "2.43ms", "stmt_id": "\\vendor\\spatie\\laravel-activitylog\\src\\ActivityLogger.php:161", "connection": "annex"}, {"sql": "select * from `loan_repayment_schedules` where `loan_repayment_schedules`.`loan_id` = 801 and `loan_repayment_schedules`.`loan_id` is not null order by `due_date` asc", "type": "query", "params": [], "bindings": ["801"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 32}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:32", "connection": "annex"}, {"sql": "select * from `loan_transactions` where `loan_id` = 801 and `loan_transaction_type_id` in (2, 6, 8) order by `submitted_on` asc, `id` asc", "type": "query", "params": [], "bindings": ["801", "2", "6", "8"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 33}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:33", "connection": "annex"}, {"sql": "select * from `loan_transactions` where `loan_id` = 801 and `loan_transaction_type_id` in (2, 6, 8) order by `submitted_on` asc, `id` asc", "type": "query", "params": [], "bindings": ["801", "2", "6", "8"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 34}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0011, "duration_str": "1.1ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:34", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11653", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11653"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00166, "duration_str": "1.66ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11654", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11654"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11655", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11655"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00145, "duration_str": "1.45ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11656", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11656"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0017900000000000001, "duration_str": "1.79ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11657", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11657"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11658", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11658"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00151, "duration_str": "1.51ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11659", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11659"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00158, "duration_str": "1.58ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11660", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11660"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0018700000000000001, "duration_str": "1.87ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11661", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11661"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0022400000000000002, "duration_str": "2.24ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11662", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11662"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11663", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11663"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00149, "duration_str": "1.49ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11664", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11664"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00166, "duration_str": "1.66ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11665", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11665"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11666", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11666"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.0014299999999999998, "duration_str": "1.43ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11667", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11667"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11668", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11668"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11669", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11669"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11670", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11670"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11671", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11671"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11672", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11672"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00126, "duration_str": "1.26ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11673", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11673"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11674", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11674"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 388, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11675", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "388", "2025-08-08 17:33:08", "11675"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `principal_repaid_derived` = 0, `interest_repaid_derived` = 0, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `total_due` = 380, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11676", "type": "query", "params": [], "bindings": ["0", "0", "0", "0", "380", "2025-08-08 17:33:08", "11676"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 43}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:43", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `paid_by_date` = '2025-08-08', `principal_repaid_derived` = 292, `interest_repaid_derived` = 96, `total_due` = 0, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11653", "type": "query", "params": [], "bindings": ["2025-08-08", "292", "96", "0", "2025-08-08 17:33:08", "11653"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 202}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:202", "connection": "annex"}, {"sql": "update `loan_repayment_schedules` set `interest_repaid_derived` = 2, `total_due` = 386, `loan_repayment_schedules`.`updated_at` = '2025-08-08 17:33:08' where `id` = 11654", "type": "query", "params": [], "bindings": ["2", "386", "2025-08-08 17:33:08", "11654"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 202}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00198, "duration_str": "1.98ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:202", "connection": "annex"}, {"sql": "update `loan_transactions` set `principal_repaid_derived` = 292, `interest_repaid_derived` = 98, `fees_repaid_derived` = 0, `penalties_repaid_derived` = 0, `loan_transactions`.`updated_at` = '2025-08-08 17:33:08' where `id` = 10053", "type": "query", "params": [], "bindings": ["292", "98", "0", "0", "2025-08-08 17:33:08", "10053"], "hints": [], "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php", "line": 211}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 432}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 255}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 197}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\Modules\\Loan\\Listeners\\UpdateTransactions.php:211", "connection": "annex"}]}, "models": {"data": {"Modules\\Loan\\Entities\\LoanTransaction": 2, "Modules\\Loan\\Entities\\LoanRepaymentSchedule": 24, "Modules\\Loan\\Entities\\LoanProduct": 1, "Modules\\Loan\\Entities\\Loan": 1, "Spatie\\Permission\\Models\\Role": 1, "Modules\\User\\Entities\\User": 1}, "count": 30}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<PERSON>@mjy3.com\"\n  \"user\" => array:24 [\n    \"id\" => 9\n    \"created_by_id\" => null\n    \"branch_id\" => null\n    \"name\" => \"\"\n    \"username\" => null\n    \"email\" => \"<PERSON>@mjy3.com\"\n    \"email_verified_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"last_login\" => null\n    \"first_name\" => \"<PERSON>\"\n    \"last_name\" => \"<PERSON>\"\n    \"phone\" => \"+233555928981\"\n    \"address\" => null\n    \"city\" => null\n    \"gender\" => \"male\"\n    \"enable_google2fa\" => 0\n    \"otp\" => null\n    \"otp_expiry_date\" => null\n    \"notes\" => \"This access is given for the purpose of internal functions only\"\n    \"photo\" => \"kI83IU45zpa8IywCrNXJY6rulxWk3OSLHuGt0dCg.jpeg\"\n    \"created_at\" => \"2024-04-10T02:12:03.000000Z\"\n    \"updated_at\" => \"2025-01-17T08:13:41.000000Z\"\n    \"full_name\" => \"<PERSON>\"\n    \"roles\" => array:1 [\n      0 => array:7 [\n        \"id\" => 4\n        \"is_system\" => 0\n        \"name\" => \"Loan Officer\"\n        \"guard_name\" => \"web\"\n        \"created_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"updated_at\" => \"2022-06-06T14:31:04.000000Z\"\n        \"pivot\" => array:3 [\n          \"model_id\" => 9\n          \"role_id\" => 4\n          \"model_type\" => \"Modules\\User\\Entities\\User\"\n        ]\n      ]\n    ]\n    \"permissions\" => []\n  ]\n]", "api": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => loan.loans.transactions.create,\n  result => true,\n  user => 9,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2038780646 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"30 characters\">loan.loans.transactions.create</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038780646\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754667188.078504}]}, "session": {"_token": "FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://*************/annex/public/loan/801/repayment/create\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"flash_notification\"\n  ]\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "9", "flash_notification": "Illuminate\\Support\\Collection {#2179\n  #items: array:1 [\n    0 => Laracasts\\Flash\\Message {#2178\n      +title: null\n      +message: \"Successfully Saved\"\n      +level: \"success\"\n      +important: true\n      +overlay: false\n    }\n  ]\n}", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"telescope": "<a href=\"http://*************/annex/public/_debugbar/telescope/9f960772-0e84-4256-ad05-fcd0640fb08e\" target=\"_blank\">View in Telescope</a>", "path_info": "/loan/801/repayment/store", "status_code": "<pre class=sf-dump id=sf-dump-1074359318 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1074359318\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1732183285 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1732183285\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">390</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-08</span>\"\n  \"<span class=sf-dump-key>payment_type_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>account_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cheque_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>routing_code</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>receipt</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>bank_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">170</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://*************</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"59 characters\">http://*************/annex/public/loan/801/repayment/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6InJxamVpdkNOb0h1cUZBQTFOYkNpQXc9PSIsInZhbHVlIjoiSFg3ZE5XUGJpNW8yL0l2TzFOaTVwT25nZjZMS244dkZ6eEZoTEpmanFCNXhVd1lzb0FqMWJFcmprdVpnbEo4L1ZxczFUeEt2VEhjL3Y0SHVxWE5qbUFSTFVVa0t0YXVhblh3d242Y0ZsaXJvT1krakQwZkRIYnAxRXBpZGRUSGsiLCJtYWMiOiJkZWM3NTIxNWVkOTE1MjRmMmUzNDU5YzdiMWY1NzYzYmI2NTlhMDRlMTY5Mzk3MmY5NDZjNGNiZTVkYWQwNDgwIn0%3D; mjy3_micro_loans_session=eyJpdiI6IjJXb1Y4bDUxY29iZ2VOTHhWYVBNSlE9PSIsInZhbHVlIjoidWtvZUsrVENvcWJBaGtvN2xrZzI2Nzg2eGw5T256M0JFVTFkYXdpSFBIL1g0TXFTYS81NGxLeXROanlYaEdzRUJwY3NlNmRLeGF5RVJMZFFDS0JVZVZObkJmeitKVlJHbUtJYzBLbHBseEZkUzNiMGxTODZ4VVRKWGhJTmdOOUQiLCJtYWMiOiJjNmMwOWY1OTRiYzkwODNhM2IzOWFmNjdkYmY0OWI3NGE2YjFiNmViZDRkYzZjY2Q1YjIxYjMyZDYzMTExNWQ1In0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-677543422 data-indent-pad=\"  \"><span class=sf-dump-note>array:54</span> [<samp>\n  \"<span class=sf-dump-key>REDIRECT_MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>REDIRECT_MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>REDIRECT_OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>REDIRECT_TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>MIBDIRS</span>\" => \"<span class=sf-dump-str title=\"24 characters\">C:/xampp/php/extras/mibs</span>\"\n  \"<span class=sf-dump-key>MYSQL_HOME</span>\" => \"<span class=sf-dump-str title=\"16 characters\">\\xampp\\mysql\\bin</span>\"\n  \"<span class=sf-dump-key>OPENSSL_CONF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/xampp/apache/bin/openssl.cnf</span>\"\n  \"<span class=sf-dump-key>PHP_PEAR_SYSCONF_DIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>PHPRC</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\php</span>\"\n  \"<span class=sf-dump-key>TMP</span>\" => \"<span class=sf-dump-str title=\"10 characters\">\\xampp\\tmp</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">170</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://*************</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"114 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"59 characters\">http://*************/annex/public/loan/801/repayment/create</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en-US</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"698 characters\">XSRF-TOKEN=eyJpdiI6InJxamVpdkNOb0h1cUZBQTFOYkNpQXc9PSIsInZhbHVlIjoiSFg3ZE5XUGJpNW8yL0l2TzFOaTVwT25nZjZMS244dkZ6eEZoTEpmanFCNXhVd1lzb0FqMWJFcmprdVpnbEo4L1ZxczFUeEt2VEhjL3Y0SHVxWE5qbUFSTFVVa0t0YXVhblh3d242Y0ZsaXJvT1krakQwZkRIYnAxRXBpZGRUSGsiLCJtYWMiOiJkZWM3NTIxNWVkOTE1MjRmMmUzNDU5YzdiMWY1NzYzYmI2NTlhMDRlMTY5Mzk3MmY5NDZjNGNiZTVkYWQwNDgwIn0%3D; mjy3_micro_loans_session=eyJpdiI6IjJXb1Y4bDUxY29iZ2VOTHhWYVBNSlE9PSIsInZhbHVlIjoidWtvZUsrVENvcWJBaGtvN2xrZzI2Nzg2eGw5T256M0JFVTFkYXdpSFBIL1g0TXFTYS81NGxLeXROanlYaEdzRUJwY3NlNmRLeGF5RVJMZFFDS0JVZVZObkJmeitKVlJHbUtJYzBLbHBseEZkUzNiMGxTODZ4VVRKWGhJTmdOOUQiLCJtYWMiOiJjNmMwOWY1OTRiYzkwODNhM2IzOWFmNjdkYmY0OWI3NGE2YjFiNmViZDRkYzZjY2Q1YjIxYjMyZDYzMTExNWQ1In0%3D</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"399 characters\">C:\\Program Files\\ImageMagick-7.1.0-Q8;C:\\windows\\system32;C:\\windows;C:\\windows\\System32\\Wbem;C:\\windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\windows\\System32\\OpenSSH\\;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\windows\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\windows</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"<span class=sf-dump-str title=\"99 characters\">&lt;address&gt;Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22 Server at ************* Port 80&lt;/address&gt;<span class=\"sf-dump-default sf-dump-ns\">\\n</span></span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.48 (Win64) OpenSSL/1.1.1k PHP/7.4.22</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"13 characters\">*************</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">*********</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"14 characters\">**************</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"15 characters\">C:/xampp/htdocs</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"20 characters\">postmaster@localhost</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"38 characters\">C:/xampp/htdocs/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">31907</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/annex/public/loan/801/repayment/store</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"38 characters\">/annex/public/loan/801/repayment/store</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/annex/public/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1754667187.1773</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1754667187</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-677543422\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1756737614 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>mjy3_micro_loans_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yPsec4PrJ2v0O17ETRQJnf9a8nwYk8R7zJ9UNPPA</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756737614\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-64277532 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 08 Aug 2025 15:33:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">http://*************/annex/public/loan/801/show</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"417 characters\">XSRF-TOKEN=eyJpdiI6InZKWTBkTmhYVExTUGZSRWNxQnVUS1E9PSIsInZhbHVlIjoiYmVlaWYrS0tzTnZHdXJPazYzT0lSWDF4Y2ZXbXkxRXk5OUltc0xDSDdHaGp4M3BTZUNwNEh2NjFtWW9MOWZIM3NMRDBIWXRzcjcvVmtSNy9OaHM1eHB2RTcxMTVvNWYzYks5bmVSZDZ4MndzNW5ySjM0NEE0NVdDS0JIWFFCSVoiLCJtYWMiOiIwMTcwYmI3ZWYxYzdjMmU1ZTBlZTZmZTE1ZDhlOWI5ZjZmMDNkMjM5MjZiMWJmZjQ2MjJiZjE3YTZjZGU5MjI3In0%3D; expires=Sat, 09-Aug-2025 11:33:08 GMT; Max-Age=72000; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">mjy3_micro_loans_session=eyJpdiI6IldySU1ZZHFrNGtZL3B1WG9LS28reHc9PSIsInZhbHVlIjoiTi94WlNXT3MvUUVoSzJXYTlrQ2RXS01PZU5PajlRa0RiclpXM2h0RmM1RzNJcjhKRFVFd2tMUElZb0o0NThabC8vNkhrYVAzd0NKZ3BVMFFyVERxVFk4Q1ZxMUZOc1B4TWdSdVI4TXhrbjc5VDZKK3hvQkVVVUZVWkJ6dnNheXkiLCJtYWMiOiIwNjgwMzBmMTM2MDNiOGYyNzU4ZDE3NjYzODZlNTdiOTBkZDI3YjZmZjNjYTE2ZDc0ZmEwYTE0MGJlZmJmYzI5In0%3D; expires=Sat, 09-Aug-2025 11:33:08 GMT; Max-Age=72000; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"388 characters\">XSRF-TOKEN=eyJpdiI6InZKWTBkTmhYVExTUGZSRWNxQnVUS1E9PSIsInZhbHVlIjoiYmVlaWYrS0tzTnZHdXJPazYzT0lSWDF4Y2ZXbXkxRXk5OUltc0xDSDdHaGp4M3BTZUNwNEh2NjFtWW9MOWZIM3NMRDBIWXRzcjcvVmtSNy9OaHM1eHB2RTcxMTVvNWYzYks5bmVSZDZ4MndzNW5ySjM0NEE0NVdDS0JIWFFCSVoiLCJtYWMiOiIwMTcwYmI3ZWYxYzdjMmU1ZTBlZTZmZTE1ZDhlOWI5ZjZmMDNkMjM5MjZiMWJmZjQ2MjJiZjE3YTZjZGU5MjI3In0%3D; expires=Sat, 09-Aug-2025 11:33:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"412 characters\">mjy3_micro_loans_session=eyJpdiI6IldySU1ZZHFrNGtZL3B1WG9LS28reHc9PSIsInZhbHVlIjoiTi94WlNXT3MvUUVoSzJXYTlrQ2RXS01PZU5PajlRa0RiclpXM2h0RmM1RzNJcjhKRFVFd2tMUElZb0o0NThabC8vNkhrYVAzd0NKZ3BVMFFyVERxVFk4Q1ZxMUZOc1B4TWdSdVI4TXhrbjc5VDZKK3hvQkVVVUZVWkJ6dnNheXkiLCJtYWMiOiIwNjgwMzBmMTM2MDNiOGYyNzU4ZDE3NjYzODZlNTdiOTBkZDI3YjZmZjNjYTE2ZDc0ZmEwYTE0MGJlZmJmYzI5In0%3D; expires=Sat, 09-Aug-2025 11:33:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-64277532\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-757920539 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FuGBCQDvgdSh10sKBLTZOZSoWUM7bMmuxgbHxjMN</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"59 characters\">http://*************/annex/public/loan/801/repayment/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">flash_notification</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>9</span>\n  \"<span class=sf-dump-key>flash_notification</span>\" => <span class=sf-dump-note title=\"Illuminate\\Support\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Collection</span> {<a class=sf-dump-ref>#2179</a><samp>\n    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note title=\"Laracasts\\Flash\\Message\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Laracasts\\Flash</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>Message</span> {<a class=sf-dump-ref>#2178</a><samp>\n        +<span class=sf-dump-public title=\"Public property\">title</span>: <span class=sf-dump-const>null</span>\n        +<span class=sf-dump-public title=\"Public property\">message</span>: \"<span class=sf-dump-str title=\"18 characters\">Successfully Saved</span>\"\n        +<span class=sf-dump-public title=\"Public property\">level</span>: \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n        +<span class=sf-dump-public title=\"Public property\">important</span>: <span class=sf-dump-const>true</span>\n        +<span class=sf-dump-public title=\"Public property\">overlay</span>: <span class=sf-dump-const>false</span>\n      </samp>}\n    </samp>]\n  </samp>}\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757920539\", {\"maxDepth\":0})</script>\n"}}
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DatabaseBackup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:backup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup the database to the Documents\mybackups\annexdb folder';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Get database configuration
        $connection = Config::get('database.default');
        $host = Config::get("database.connections.{$connection}.host");
        $port = Config::get("database.connections.{$connection}.port");
        $database = Config::get("database.connections.{$connection}.database");
        $username = Config::get("database.connections.{$connection}.username");
        $password = Config::get("database.connections.{$connection}.password");

        // Create timestamp for filename
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $filename = "{$database}_{$timestamp}.sql";

        // Ensure the backup directory exists
        $backupPath = Storage::disk('documents')->path('');
        if (!file_exists($backupPath)) {
            mkdir($backupPath, 0755, true);
            $this->info("Created backup directory: {$backupPath}");
        }

        // Build mysqldump command
        $command = sprintf(
            'mysqldump --host=%s --port=%s --user=%s --password=%s %s > %s',
            escapeshellarg($host),
            escapeshellarg($port),
            escapeshellarg($username),
            escapeshellarg($password),
            escapeshellarg($database),
            escapeshellarg(Storage::disk('documents')->path($filename))
        );

        // Execute the command
        $this->info("Backing up database to {$backupPath}...");
        $returnVar = NULL;
        $output = NULL;
        exec($command, $output, $returnVar);

        if ($returnVar !== 0) {
            $this->error("Database backup failed!");
            return 1;
        }

        $this->info("Database backup completed successfully!");
        $this->info("Backup saved to: " . Storage::disk('documents')->path($filename));

        return 0;
    }
}
